package com.chinamobile.iot.sc.enums;

/**
 * 省侧装维平台枚举
 */
public enum ProvinceInstallPlatformEnum {

    HENAN("henan", "二级业务编排系统(河南)", "河南省"),
    SHANDONG("shandong", "二级业务编排系统(山东)", "山东省"),
    GUANGDONG("guangdong", "二级业务编排系统(广东)","广东省"),
    JIANGSU("jiangsu", "二级业务编排系统(江苏)", "江苏省"),
    ;
    private String code;

    private String name;

    private String provinceName;

    ProvinceInstallPlatformEnum(String code, String name, String provinceName) {
        this.code = code;
        this.name = name;
        this.provinceName = provinceName;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public String getProvinceName() {
        return provinceName;
    }

    public static String getName(String code) {
        for (ProvinceInstallPlatformEnum provinceInstallPlatformEnum : ProvinceInstallPlatformEnum.values()) {
            if (provinceInstallPlatformEnum.getCode().equals(code)) {
                return provinceInstallPlatformEnum.getName();
            }
        }
        return null;
    }

    public static String getProvinceName(String code) {
        for (ProvinceInstallPlatformEnum provinceInstallPlatformEnum : ProvinceInstallPlatformEnum.values()) {
            if (provinceInstallPlatformEnum.getCode().equals(code)) {
                return provinceInstallPlatformEnum.getProvinceName();
            }
        }
        return null;
    }

    public static boolean contains(String code) {
        for (ProvinceInstallPlatformEnum provinceInstallPlatformEnum : ProvinceInstallPlatformEnum.values()) {
            if (provinceInstallPlatformEnum.getCode().equals(code)) {
                return true;
            }
        }
        return false;
    }

    public static String allCode(){
        StringBuilder sb = new StringBuilder();
        for (ProvinceInstallPlatformEnum provinceInstallPlatformEnum : ProvinceInstallPlatformEnum.values()) {
            sb.append(provinceInstallPlatformEnum.getCode()).append(",");
        }
        return sb.substring(0, sb.length() - 1);
    }
}
