package com.chinamobile.install.feign;


import com.chinamobile.iot.sc.entity.b2b.CommonProvinceSyncParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/07/28
 * @description 江苏能力开放平台接口
 */
@Service
@FeignClient(name = "jiangsu-open-feign-client", url = "${zhuangwei.url.jiangsuOpenUrl}")
public interface JiangSuZwOpenFeignClient {

    @PostMapping("/ser-compose-busi-common-js/api/crm/v1/passNewWorkSheet")
    String sendZwOrder(
        @RequestBody CommonProvinceSyncParam body
    );
}