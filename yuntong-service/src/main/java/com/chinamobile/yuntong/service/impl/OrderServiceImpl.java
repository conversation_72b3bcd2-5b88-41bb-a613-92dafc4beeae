package com.chinamobile.yuntong.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.Constant;
import com.chinamobile.iot.sc.common.redis.RedisUtil;
import com.chinamobile.iot.sc.common.utils.BaseServiceUtils;
import com.chinamobile.iot.sc.exceptions.BaseErrorConstant;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.iot.sc.util.DateUtils;
import com.chinamobile.yuntong.config.RestTemplateConfig;
import com.chinamobile.yuntong.dao.YuntongOrderInfoMapper;
import com.chinamobile.yuntong.dao.YuntongOrderMaterialMapper;
import com.chinamobile.yuntong.dao.ext.YuntongOrderInfoMapperExt;
import com.chinamobile.yuntong.excel.SyncOrderToMarketSystemCutOver;
import com.chinamobile.yuntong.exception.ServicePowerException;
import com.chinamobile.yuntong.exception.StatusContant;
import com.chinamobile.yuntong.pojo.dto.OrderInfoListExcelDTO;
import com.chinamobile.yuntong.pojo.entity.YuntongOrderInfo;
import com.chinamobile.yuntong.pojo.entity.YuntongOrderInfoExample;
import com.chinamobile.yuntong.pojo.entity.YuntongOrderMaterial;
import com.chinamobile.yuntong.pojo.entity.YuntongOrderMaterialExample;
import com.chinamobile.yuntong.pojo.mapper.OrderInfoListDO;
import com.chinamobile.yuntong.pojo.param.OrderInfoListExportParam;
import com.chinamobile.yuntong.pojo.param.OrderInfoListParam;
import com.chinamobile.yuntong.pojo.param.OrderInfoParam;
import com.chinamobile.yuntong.pojo.param.OrderMaterialParam;
import com.chinamobile.yuntong.pojo.vo.OrderInfoListVO;
import com.chinamobile.yuntong.pojo.vo.OrderMaterialListVO;
import com.chinamobile.yuntong.request.SoftwareOrderInfoSyncRequest;
import com.chinamobile.yuntong.request.SyncCommonRequest;
import com.chinamobile.yuntong.response.iot.SoftwareOrderInfoSyncResponse;
import com.chinamobile.yuntong.service.OrderService;
import com.chinamobile.yuntong.util.YuntongSignUtils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Hex;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.concurrent.CustomizableThreadFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * created by liuxiang on 2025/1/8 11:03
 */
@Slf4j
@Service
public class OrderServiceImpl implements OrderService {

    @Resource
    private YuntongOrderInfoMapperExt orderInfoMapperExt;
    @Resource
    private YuntongOrderInfoMapper orderInfoMapper;

    @Resource
    private YuntongOrderMaterialMapper orderMaterialMapper;
    @Resource
    private RedisUtil redisUtil;
    @Resource
    private RedisTemplate redisTemplate;
    @Value("${scm.marketSystem.key}")
    private String marketSystemKey;

    @Value("${scm.marketSystem.secret}")
    private String marketSystemSecret;

    @Value("${scm.marketSystem.orderSyncUrl}")
    private String orderSyncUrl;

    @Value("${supply.des.key}")
    private String desKey;
    public final static String REDIS_MARKET_YUNTONG_KEY = "SC:MARKET:YUNTONG";

    ThreadFactory springThreadFactory = new CustomizableThreadFactory("springThread-pool-");
    ThreadPoolExecutor executor = new ThreadPoolExecutor(4, 10, 4L, TimeUnit.MILLISECONDS,
            new LinkedBlockingQueue<>(100000), springThreadFactory);


    @Override
    public BaseAnswer<Void> orderInfo(SyncCommonRequest syncCommonRequest) {
        log.info("云瞳订单同步请求:{}", JSON.toJSONString(syncCommonRequest));
        Date now = new Date();
        BaseAnswer<Void> baseAnswer = new BaseAnswer<>();
        String input = syncCommonRequest.getInput();
        String sign = syncCommonRequest.getSign();
//      sign验签
        try {
            YuntongSignUtils.checkSign(input, sign);
        } catch (ServicePowerException e) {
            throw new BusinessException(StatusContant.INTERNAL_ERROR, e.getMessage());
        }
        OrderInfoParam orderInfoParam;
        try {
            orderInfoParam = JSON.parseObject(input, OrderInfoParam.class);
        } catch (Exception e) {
            log.error("解析异常:" + e);
            baseAnswer.setMessage("input解析错误");
            baseAnswer.setStateCode("10004");
            return baseAnswer;
        }

        // 先存库，然后根据时间再算要不要给过去
        String orderId = orderInfoParam.getMaterialList().get(0).getOrderNumber();
        if(!orderInfoParam.getMaterialList().stream()
                .filter(Objects::nonNull)
                .map(OrderMaterialParam::getOrderNumber)
                .allMatch(orderNum ->
                                (orderId != null && orderId.equals(orderNum))
                )){
            baseAnswer.setMessage("物料列表中存在不同订单号");
            baseAnswer.setStateCode("10004");
            return baseAnswer;
        }
        YuntongOrderInfo exist = orderInfoMapper.selectByPrimaryKey(orderId);
        if(exist != null){
            baseAnswer.setMessage("该订单已存在");
            baseAnswer.setStateCode("10004");
            return baseAnswer;
        }
        YuntongOrderInfo yuntongOrderInfo = new YuntongOrderInfo();
        BeanUtils.copyProperties(orderInfoParam, yuntongOrderInfo);
        yuntongOrderInfo.setSettleStatus("0");
        yuntongOrderInfo.setOrderId(orderId);
        List<YuntongOrderMaterial> yuntongOrderMaterialList = new ArrayList<>();
        orderInfoParam.getMaterialList().forEach(item -> {
            YuntongOrderMaterial yuntongOrderMaterial = new YuntongOrderMaterial();
            BeanUtils.copyProperties(item, yuntongOrderMaterial);
            yuntongOrderMaterial.setId(BaseServiceUtils.getId());
            yuntongOrderMaterialList.add(yuntongOrderMaterial);
            }
        );

        orderInfoMapper.insert(yuntongOrderInfo);
        orderMaterialMapper.batchInsert(yuntongOrderMaterialList);

        // 自然月倒数第2天12：00：00-自然月底23 ：59：59不同步，后面跑定时任务再同步
        // 时间以同步过来时间为准 下个月跑上个月没同步的所有单子 这样应该不会漏
        LocalDateTime dateNow = LocalDateTime.now();
        if(!isWithinLastTwoDaysOfMonth(dateNow)){
            executor.execute(() -> {
                try {
                    Thread.sleep(10 * 1000L);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
                syncOrderToMarketSystem(orderId);
            });
        }

        return baseAnswer;
    }

    /**
     * 同步订单数据给市场销售支撑系统B2B
     * @param OrderId
     * @return
     */
    @Override
    public BaseAnswer<Void> syncOrderToMarketSystem(String OrderId) {
        BaseAnswer<Void> baseAnswer = new BaseAnswer<>();
        return redisUtil.smartLock(REDIS_MARKET_YUNTONG_KEY, () -> {
            SoftwareOrderInfoSyncRequest softwareOrderInfoSyncRequest = new SoftwareOrderInfoSyncRequest();
            SoftwareOrderInfoSyncRequest.ROOT root = new SoftwareOrderInfoSyncRequest.ROOT();
            softwareOrderInfoSyncRequest.setROOT(root);
            SoftwareOrderInfoSyncRequest.HEADER header = new SoftwareOrderInfoSyncRequest.HEADER();
            root.setHEADER(header);
            SoftwareOrderInfoSyncRequest.BODY body = new SoftwareOrderInfoSyncRequest.BODY();
            root.setBODY(body);
            SoftwareOrderInfoSyncRequest.AuthInfo authInfo = new SoftwareOrderInfoSyncRequest.AuthInfo();
            SoftwareOrderInfoSyncRequest.BusiInfo busiInfo = new SoftwareOrderInfoSyncRequest.BusiInfo();
            String timestamp = DateUtils.dateToStr(new Date(), DateUtils.DATETIME_FORMAT_NO_SYMBOL);
            String signBefore = marketSystemKey + "|" + marketSystemSecret +"|"+timestamp;
            String sign = null;
            try {
                sign = encrypt(signBefore, marketSystemSecret);
            } catch (Exception e) {
                throw new BusinessException(StatusContant.INTERNAL_ERROR, e.getMessage());
            }
            authInfo.setUSER_ID(marketSystemKey);
            authInfo.setPOST_DATE(DateUtils.dateToStr(new Date(), DateUtils.DATETIME_FORMAT_NO_SYMBOL));
            authInfo.setSIGN(sign);
            body.setAUTH_INFO(authInfo);
            body.setBUSI_INFO(busiInfo);
            List<SoftwareOrderInfoSyncRequest.OrderHeaderInfo> orderHeaderInfoList = new ArrayList<>();
            busiInfo.setRECEIVABLE_INFO_LIST(orderHeaderInfoList);
            Integer billNoCount = (Integer) redisTemplate.opsForValue().get(Constant.REDIS_KEY_BILL_NO_YUNTONG);
            String billNoNumber = null;

            YuntongOrderInfo yuntongOrderInfo = orderInfoMapper.selectByPrimaryKey(OrderId);
            List<YuntongOrderMaterial> yuntongOrderMaterialList = orderMaterialMapper.selectByExample(
              new YuntongOrderMaterialExample().createCriteria().andOrderNumberEqualTo(OrderId).example()
            );
            SoftwareOrderInfoSyncRequest.OrderHeaderInfo orderHeaderInfo = new SoftwareOrderInfoSyncRequest.OrderHeaderInfo();
            setAllFieldsToEmptyString(orderHeaderInfo);
            // 物料列表
            List<SoftwareOrderInfoSyncRequest.SaleLineInfo> saleLineInfoList = new ArrayList<>();

            // 增加重复同步校验机制
            if(StringUtils.isNotEmpty(yuntongOrderInfo.getBillNoNumber())){
                log.info("云瞳请求市场销售支撑系统接口失败:订单已同步过市场销售系统");
                baseAnswer.setMessage("云瞳订单已同步过市场销售系统");
                baseAnswer.setStateCode("10004");
                return baseAnswer;
            }
            yuntongOrderMaterialList.stream().forEach(item->{
                SoftwareOrderInfoSyncRequest.SaleLineInfo saleLineInfo = new SoftwareOrderInfoSyncRequest.SaleLineInfo();
                saleLineInfo.setAMOUNT(BigDecimal.valueOf(item.getAmount()));
                saleLineInfo.setCOST_AMOUNT(new BigDecimal(0));
                saleLineInfo.setENTRY_DISCOUNT_RATE(new BigDecimal(0));
                saleLineInfo.setDIS_COUNT_AMOUNT_FOR(new BigDecimal(0));
                saleLineInfo.setMODEL(item.getModel());
                saleLineInfo.setNO_TAX_PRICE(new BigDecimal(item.getNotaxprice()));
                saleLineInfo.setORDER_NUMBER(item.getOrderNumber());
                saleLineInfo.setPRICE(new BigDecimal(item.getPrice()));
                saleLineInfo.setPRODUCT_NAME(item.getProductName());
                saleLineInfo.setPRODUCT_NUMBER(item.getProductNumber());
                saleLineInfo.setTAX(new BigDecimal(item.getTax()));
                saleLineInfo.setTAX_PRICE(new BigDecimal(item.getTaxprice()));
                saleLineInfo.setTAX_RATE(new BigDecimal(item.getTaxrate()));
                saleLineInfo.setUNIT(item.getUnit());
                // 类好像有点问题 后面重新生成
                saleLineInfo.setVALOREM_TOTAL(new BigDecimal(item.getValoremTotal()));
                saleLineInfoList.add(saleLineInfo);
            });

            orderHeaderInfo.setMATERIAL_LIST(saleLineInfoList);

            if (null == billNoCount) {
                orderHeaderInfo.setBILL_NO("AMY00000000");
                billNoNumber = "AMY00000000";
                billNoCount = 1;
            }else{
                orderHeaderInfo.setBILL_NO("AMY"+String.format("%08d",billNoCount));
                billNoNumber = "AMY"+String.format("%08d",billNoCount);
                billNoCount++;
            }
            orderHeaderInfo.setBUSINESS_DATE(DateUtils.dateToStr(new Date(), DateUtils.DEFAULT_DATETIME_FORMAT));

            orderHeaderInfo.setCOMPANY("0390028704");
            orderHeaderInfo.setCOMPANY_NAME("物联网公司");
            orderHeaderInfo.setCURRENCY("PRE001");
            orderHeaderInfo.setBILL_TYPE_ID("1");

            orderHeaderInfo.setAPPROVE_DATE(DateUtils.dateToStr(new Date(), DateUtils.DEFAULT_DATETIME_FORMAT));
            orderHeaderInfo.setAPPROVE_ID(yuntongOrderInfo.getApproveId());

            orderHeaderInfo.setCLIENT(yuntongOrderInfo.getClient());
            orderHeaderInfo.setCLIENT_NAME(aesDecrypt(yuntongOrderInfo.getClientName(),desKey));
            orderHeaderInfo.setSALE_ORG_ID(yuntongOrderInfo.getSaleOrgId());
            orderHeaderInfo.setSALE_ORG_NAME(yuntongOrderInfo.getSaleOrgName());
            orderHeaderInfo.setCOST_CENTER_CODE(yuntongOrderInfo.getCostCenterCode());
            orderHeaderInfo.setCOST_CENTER_NAME(yuntongOrderInfo.getCostCenterName());
            orderHeaderInfo.setSALES_MAN(yuntongOrderInfo.getSalesman());
            orderHeaderInfo.setSALES_MAN_NAME(yuntongOrderInfo.getSalesmanName());
            orderHeaderInfo.setPAY_ORG_ID(yuntongOrderInfo.getPayOrgId());
            orderHeaderInfo.setPAY_ORG_NAME("物联网公司");

            orderHeaderInfo.setRED_BLUE(yuntongOrderInfo.getRedBlue());
            orderHeaderInfo.setDOCUMENT_STATUS("A");
            orderHeaderInfo.setYES_OR_NO_SERVICE("true");
            orderHeaderInfo.setPROVINCE("重庆");
            orderHeaderInfo.setCITY("重庆");
            orderHeaderInfo.setBUSINESS_CATEGORY("U");
            orderHeaderInfo.setBUSINESS_MODE("standard");
            orderHeaderInfo.setRESPONSIBLE_PERSON(yuntongOrderInfo.getResponsiblePerson());


            orderHeaderInfo.setSALE_DEPT_ID(yuntongOrderInfo.getSaleDeptId());

            orderHeaderInfo.setSAL_ORDER_DATE(yuntongOrderInfo.getBusinessDate().toString());

            orderHeaderInfo.setMALL_ORDER_TYPE("1");

            orderHeaderInfo.setCONTRACT("YUNTONG-CONTRACT");

            orderHeaderInfo.setVALOREM_TOTAL(new BigDecimal(yuntongOrderInfo.getValoremTotal()));
            orderHeaderInfo.setNO_TAX_AMOUNT_FOR(yuntongOrderInfo.getNoTaxAmountFor());
            orderHeaderInfo.setTAX_AMOUNT_FOR(yuntongOrderInfo.getTaxAmountFor());


            orderHeaderInfoList.add(orderHeaderInfo);

            System.out.println("云瞳生成的body: "+ JSONObject.toJSONString(softwareOrderInfoSyncRequest));


            // 请求市场销售支撑系统
            try {
                HttpHeaders headers = new HttpHeaders();
                headers.add("content-type", "application/json;charset=utf-8");

                HttpEntity<String> requestEntity = new HttpEntity<>(JSON.toJSONString(softwareOrderInfoSyncRequest), headers);

                log.info("云瞳请求市场销售支撑系统接口request:{}",JSON.toJSONString(requestEntity));
                RestTemplate restTemplateHttps = new RestTemplate(RestTemplateConfig.generateHttpRequestFactory());
                ResponseEntity<String> response = restTemplateHttps.postForEntity(orderSyncUrl, requestEntity, String.class);
                log.info("云瞳请求市场销售支撑系统接口response:{}",JSON.toJSONString(response));
                SoftwareOrderInfoSyncResponse response1 = JSON.parseObject(response.getBody(), SoftwareOrderInfoSyncResponse.class);
                if(!response1.getROOT().getBODY().getRETURN_CODE().equals("0")){
                    log.info("云瞳请求市场销售支撑系统接口失败:{}", response1.getROOT().getBODY().getDETAIL_MSG());
                    baseAnswer.setMessage(response1.getROOT().getBODY().getDETAIL_MSG());
                    baseAnswer.setStateCode("10004");
                    yuntongOrderInfo.setSettleStatus("2");
                    orderInfoMapper.updateByPrimaryKeySelective(yuntongOrderInfo);
                }else{
                    redisTemplate.opsForValue().set(Constant.REDIS_KEY_BILL_NO_YUNTONG, billNoCount);
                    // 订单表存一下
                    yuntongOrderInfo.setBillNoNumber(billNoNumber);
                    yuntongOrderInfo.setSettleStatus("1");
                    yuntongOrderInfo.setBillNoTime(new Date());
                    orderInfoMapper.updateByPrimaryKeySelective(yuntongOrderInfo);
                }
            }catch (Exception e) {
            }

            return baseAnswer;
        });
    }
    public  boolean isWithinLastTwoDaysOfMonth(LocalDateTime dateTime) {
        LocalDate currentDate = dateTime.toLocalDate();
        LocalDate lastDayOfMonth = currentDate.withDayOfMonth(currentDate.lengthOfMonth());
        LocalDate secondLastDayOfMonth = lastDayOfMonth.minusDays(1);

        LocalDateTime startTime = LocalDateTime.of(secondLastDayOfMonth, LocalTime.NOON); // 12:00:00
        LocalDateTime endTime = LocalDateTime.of(lastDayOfMonth, LocalTime.MAX); // 23:59:59.999999999

        return !dateTime.isBefore(startTime) && !dateTime.isAfter(endTime);
    }

    public String encrypt(String password, String strKey) throws
            Exception{
        byte[] keyBytes = Arrays.copyOf(strKey.getBytes("ASCII"), 16);
        SecretKey key = new SecretKeySpec(keyBytes, "AES");
        Cipher cipher = Cipher.getInstance("AES");
        cipher.init(Cipher.ENCRYPT_MODE, key);
        byte[] cleartext = password.getBytes("UTF-8");
        byte[] ciphertextBytes = cipher.doFinal(cleartext);
        return new String(Hex.encodeHex(ciphertextBytes));
    }

    // 使用AES算法解密数据
    public static String aesDecrypt(String input, String key) {
        try {
            Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
            SecretKeySpec secretKeySpec = new SecretKeySpec(key.getBytes(), "AES");
            cipher.init(Cipher.DECRYPT_MODE, secretKeySpec);
            byte[] decryptedBytes = cipher.doFinal(Base64.getDecoder().decode(input));
            return new String(decryptedBytes);
        } catch (Exception e) {
            log.error("解密失败，解密原文: {}", input, e);
            return input; // 返回原文
        }

    }

    public static void setAllFieldsToEmptyString(Object obj) {
        try {
            Field[] fields = obj.getClass().getDeclaredFields();
            for (Field field : fields) {
                field.setAccessible(true);
                if (field.getType() == String.class) {
                    Object value = field.get(obj);
                    if (value == null) {
                        field.set(obj, "");
                    }
                } else if (field.getType().isArray() && field.getType().getComponentType() == String.class) {
                    Object value = field.get(obj);
                    if (value == null) {
                        field.set(obj, new String[0]);
                    }
                }
            }
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        }
    }

    @Override
    public BaseAnswer syncOrderToMarketSystemCutOver(MultipartFile excel) {
        List<String> errMsg = new ArrayList<>();
        try {
            List<SyncOrderToMarketSystemCutOver> orderList = EasyExcel.read(excel.getInputStream(), SyncOrderToMarketSystemCutOver.class, null).sheet(0).headRowNumber(1).doReadSync();
            for(SyncOrderToMarketSystemCutOver item : orderList){
                //看那边有没有并发限制
                executor.execute(() -> syncOrderToMarketSystem(item.getOrderId()));
            }
        } catch (IOException e) {
            // 该服务仅供内部开发人员自己调用，异常不用理会
            throw new RuntimeException(e);
        }
        if (errMsg.isEmpty()) {
            return BaseAnswer.success("");
        } else {
            return BaseAnswer.success(errMsg);
        }
    }

    @Override
    public void syncOrderToMarketSystemLastMonth() {
        // 获取上月的起始日期和结束日期
        Calendar calendar = Calendar.getInstance();

        // 设置为上月的第一天 0 点
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.add(Calendar.MONTH, -1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        Date startDate = calendar.getTime(); // 上月初 0 点

        // 设置为现在 为了防止后续变为交易成功后时间跨越的情况
        Date endDate = new Date();

        //todo 看下时间格式
        List<YuntongOrderInfo> orderInfoList = orderInfoMapper.selectByExample(
                new YuntongOrderInfoExample().createCriteria()
                        .andBillNoNumberIsNull()
                        .andBusinessDateBetween(startDate,endDate)
                .example()
        );
        log.info("orderIdList is :{}",orderInfoList);
        for(YuntongOrderInfo orderInfo : orderInfoList){
            executor.execute(() -> syncOrderToMarketSystem(orderInfo.getOrderId()));
        }
    }

    @Override
    public BaseAnswer syncOrderToMarketSystemCutOverClear(MultipartFile excel) {
        List<String> errMsg = new ArrayList<>();
        try {
            List<SyncOrderToMarketSystemCutOver> orderList = EasyExcel.read(excel.getInputStream(), SyncOrderToMarketSystemCutOver.class, null).sheet(0).headRowNumber(1).doReadSync();
            for(SyncOrderToMarketSystemCutOver item : orderList){
                //看那边有没有并发限制
                executor.execute(() -> {
                    YuntongOrderInfo order2cInfo = orderInfoMapper.selectByPrimaryKey(item.getOrderId());
                    order2cInfo.setBillNoNumber(null);
                    orderInfoMapper.updateByPrimaryKey(order2cInfo);
                });
            }
        } catch (IOException e) {
            // 该服务仅供内部开发人员自己调用，异常不用理会
            throw new RuntimeException(e);
        }
        if (errMsg.isEmpty()) {
            return BaseAnswer.success("");
        } else {
            return BaseAnswer.success(errMsg);
        }
    }

    @Override
    public BaseAnswer<PageData<OrderInfoListVO>> orderInfoList(OrderInfoListParam param) {
        PageData<OrderInfoListVO> pageData = new PageData<>();
        Integer pageNum = param.getPageNum();
        Integer pageSize = param.getPageSize();
        pageData.setPage(pageNum);
        PageHelper.startPage(pageNum,pageSize);
        List<OrderInfoListDO> list = orderInfoMapperExt.orderInfoList(param);
        if(CollectionUtils.isEmpty(list)){
            pageData.setCount(0L);
            return BaseAnswer.success(pageData);
        }
        List<OrderInfoListVO> voList = list.stream().map(o -> {
            OrderInfoListVO vo = new OrderInfoListVO();
            BeanUtils.copyProperties(o, vo);
            List<YuntongOrderMaterial> materialList = orderMaterialMapper.selectByExample(
                    new YuntongOrderMaterialExample().createCriteria()
                            .andOrderNumberEqualTo(o.getOrderId())
                            .example()
            );
            List<OrderMaterialListVO> orderMaterialListVOList = new ArrayList<>();
            materialList.stream().forEach(item->{
                OrderMaterialListVO orderMaterialListVO = new OrderMaterialListVO();
                BeanUtils.copyProperties(item, orderMaterialListVO);
                orderMaterialListVOList.add(orderMaterialListVO);
            });
            vo.setMaterialList(orderMaterialListVOList);
            return vo;
        }).collect(Collectors.toList());
        PageInfo<OrderInfoListDO> pageInfo = new PageInfo<>(list);
        pageData.setCount(pageInfo.getTotal());
        pageData.setData(voList);
        return BaseAnswer.success(pageData);
    }

    @Override
    public void exportOrderInfoList(OrderInfoListExportParam param) {
        List<OrderInfoListExcelDTO> list = orderInfoMapperExt.orderInfoListExport(param);
        if (CollectionUtils.isEmpty(list)) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "无数据");
        }

        ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        HttpServletResponse response = requestAttr.getResponse();

        try {
//            int[] mergeColIndex = {0,6,7,8,9};
//            int mergeRowIndex = 1;
//            int colIndex = 0;
            EasyExcel.write(response.getOutputStream(),OrderInfoListExcelDTO.class)
                    .sheet("订单导出")
                    .registerWriteHandler(new ActivityService.ExcelWidthStyleStrategy())
                    .doWrite(list);
            String name = URLEncoder.encode("订单列表.xlsx", "UTF-8");
            response.setHeader("Access-Control-Expose-Headers", "content-disposition");
            response.setHeader("content-type", "application/octet-stream;utf-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + name);
            response.setCharacterEncoding("utf-8");
            response.addHeader("stateCode", "00000");
//            String excelName = "订单列表";
//            excelName = URLEncoder.encode(excelName, "UTF-8");
//            ClassPathResource classPathResource = new ClassPathResource("templates/order_export.xlsx");
//            InputStream templateFileName = classPathResource.getInputStream();
//            String stateCode = BaseErrorConstant.PARAM_ERROR.getStateCode();
//            String message = BaseErrorConstant.PARAM_ERROR.getMessage();
//            log.info("orderIdList is :{}",list);
//            //构建填充excel参数
//            Map<String, Object> map = new HashMap<String, Object>();
//            EasyExcelUtils.exportExcel(response, "list", list, map, excelName, templateFileName,
//                    0, "订单列表", stateCode, message);
//            response.setHeader("Access-Control-Expose-Headers", "content-disposition");
//            response.setHeader("content-type", "application/octet-stream;utf-8");;
//            response.setCharacterEncoding("utf-8");
//            response.addHeader("stateCode", "00000");
        } catch (Exception e) {
            response.addHeader("stateCode","99999");
            response.addHeader("message", e.getMessage());
            log.info("订单列表导出失败！", e);
            throw new BusinessException(StatusContant.PARAM_ERROR, "导出失败");
        }
    }

}
