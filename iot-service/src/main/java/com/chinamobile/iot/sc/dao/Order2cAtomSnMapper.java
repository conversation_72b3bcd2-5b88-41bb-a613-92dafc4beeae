package com.chinamobile.iot.sc.dao;

import com.chinamobile.iot.sc.pojo.Order2cAtomSn;
import com.chinamobile.iot.sc.pojo.Order2cAtomSnExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface Order2cAtomSnMapper {
    long countByExample(Order2cAtomSnExample example);

    int deleteByExample(Order2cAtomSnExample example);

    int insert(Order2cAtomSn record);

    int insertSelective(Order2cAtomSn record);

    List<Order2cAtomSn> selectByExample(Order2cAtomSnExample example);

    int updateByExampleSelective(@Param("record") Order2cAtomSn record, @Param("example") Order2cAtomSnExample example);

    int updateByExample(@Param("record") Order2cAtomSn record, @Param("example") Order2cAtomSnExample example);

    int batchInsert(@Param("list") List<Order2cAtomSn> list);

    int batchInsertSelective(@Param("list") List<Order2cAtomSn> list, @Param("selective") Order2cAtomSn.Column ... selective);
}